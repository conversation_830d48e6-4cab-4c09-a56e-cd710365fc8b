// User types
export interface User {
  id: string
  email: string
  name: string
  avatar_url?: string
  theme_preference: 'light' | 'dark'
  timezone: string
  created_at: string
  updated_at: string
}

// Habit types
export type HabitCategory = 
  | 'health' 
  | 'productivity' 
  | 'learning' 
  | 'fitness' 
  | 'mindfulness' 
  | 'social' 
  | 'creative' 
  | 'financial'

export type HabitFrequency = 'daily' | 'weekly' | 'custom'

export interface Habit {
  id: string
  user_id: string
  name: string
  description?: string
  category: HabitCategory
  frequency: HabitFrequency
  target_days?: number
  current_streak: number
  longest_streak: number
  completion_rate: number
  is_active: boolean
  created_at: string
}

export interface HabitEntry {
  id: string
  habit_id: string
  date: string
  completed: boolean
  notes?: string
  created_at: string
}

// Goal types
export type GoalCategory = 
  | 'financial' 
  | 'health' 
  | 'career' 
  | 'personal' 
  | 'education' 
  | 'relationships'

export type GoalTimeframe = 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly'
export type GoalStatus = 'active' | 'completed' | 'paused' | 'cancelled'

export interface Goal {
  id: string
  user_id: string
  title: string
  description: string
  category: GoalCategory
  timeframe: GoalTimeframe
  target_date: string
  progress: number
  status: GoalStatus
  parent_goal_id?: string
  created_at: string
  updated_at: string
}

export interface Milestone {
  id: string
  goal_id: string
  title: string
  description?: string
  target_date: string
  completed: boolean
  completed_at?: string
  created_at: string
}

// Financial types
export type AccountType = 'checking' | 'savings' | 'investment' | 'credit' | 'loan'

export interface FinancialProfile {
  id: string
  user_id: string
  net_worth: number
  total_assets: number
  total_liabilities: number
  monthly_income: number
  monthly_expenses: number
  last_updated: string
}

export interface FinancialAccount {
  id: string
  user_id: string
  account_type: AccountType
  institution_name?: string
  account_name: string
  balance: number
  is_connected: boolean
  external_account_id?: string
  created_at: string
}

export interface FinancialMilestone {
  id: string
  user_id: string
  title: string
  target_amount: number
  current_amount: number
  target_date: string
  achieved: boolean
  achieved_at?: string
  created_at: string
}

// Performance Metrics types
export type MetricCategory = 'health' | 'wealth' | 'relationships' | 'skills' | 'productivity'
export type TrendDirection = 'up' | 'down' | 'stable'

export interface PerformanceMetric {
  id: string
  user_id: string
  name: string
  category: MetricCategory
  current_value: number
  target_value: number
  unit: string
  trend: TrendDirection
  last_updated: string
  created_at: string
}

export interface MetricEntry {
  id: string
  metric_id: string
  value: number
  date: string
  notes?: string
  created_at: string
}

// Time Management types
export type TimeCategory = 
  | 'work' 
  | 'learning' 
  | 'exercise' 
  | 'social' 
  | 'entertainment' 
  | 'personal' 
  | 'commute' 
  | 'sleep'

export interface TimeEntry {
  id: string
  user_id: string
  activity: string
  category: TimeCategory
  start_time: string
  end_time: string
  duration: number
  productivity_score: number
  notes?: string
  created_at: string
}

// Dashboard types
export interface DashboardStats {
  habits: {
    total: number
    completed_today: number
    current_streaks: number
    completion_rate: number
  }
  goals: {
    total: number
    completed: number
    in_progress: number
    completion_rate: number
  }
  financial: {
    net_worth: number
    monthly_growth: number
    next_milestone: FinancialMilestone | null
  }
  performance: {
    average_score: number
    improving_metrics: number
    declining_metrics: number
  }
}

// Chart data types
export interface ChartDataPoint {
  date: string
  value: number
  label?: string
}

export interface HeatmapData {
  date: string
  count: number
  level: 0 | 1 | 2 | 3 | 4
}

// API Response types
export interface ApiResponse<T> {
  data: T | null
  error: string | null
  success: boolean
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  has_more: boolean
}
