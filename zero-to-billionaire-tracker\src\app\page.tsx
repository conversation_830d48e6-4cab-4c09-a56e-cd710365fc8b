import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Target, TrendingUp, CheckSquare, DollarSign, Clock, Zap } from 'lucide-react'

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted">
      {/* Hero Section */}
      <div className="container mx-auto px-4 py-16">
        <div className="text-center space-y-8">
          {/* Logo and Title */}
          <div className="flex items-center justify-center space-x-4">
            <div className="h-16 w-16 rounded-xl bg-gradient-to-br from-gold to-gold-dark flex items-center justify-center">
              <span className="text-white font-bold text-2xl">Z2B</span>
            </div>
            <div className="text-left">
              <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent">
                Zero to Billionaire
              </h1>
              <p className="text-xl text-muted-foreground">Success Tracker</p>
            </div>
          </div>

          {/* Subtitle */}
          <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto">
            Track your journey to extraordinary success with comprehensive habit tracking,
            goal management, financial monitoring, and performance analytics.
          </p>

          {/* CTA Button */}
          <div className="flex justify-center space-x-4">
            <Link href="/dashboard">
              <Button size="lg" variant="premium" className="text-lg px-8 py-6">
                Start Your Journey
                <Zap className="ml-2 h-5 w-5" />
              </Button>
            </Link>
          </div>
        </div>

        {/* Features Grid */}
        <div className="mt-20 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <Card className="border-2 hover:border-primary/50 transition-colors">
            <CardHeader>
              <CheckSquare className="h-12 w-12 text-primary mb-4" />
              <CardTitle>Habit Tracking</CardTitle>
              <CardDescription>
                Build consistent routines with daily check-ins, streak counters, and visual progress indicators.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="border-2 hover:border-primary/50 transition-colors">
            <CardHeader>
              <Target className="h-12 w-12 text-primary mb-4" />
              <CardTitle>Goal Achievement</CardTitle>
              <CardDescription>
                Set SMART goals, track progress, and celebrate milestones on your path to success.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="border-2 hover:border-primary/50 transition-colors">
            <CardHeader>
              <DollarSign className="h-12 w-12 text-primary mb-4" />
              <CardTitle>Financial Progress</CardTitle>
              <CardDescription>
                Monitor net worth, track investments, and visualize your journey to financial freedom.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="border-2 hover:border-primary/50 transition-colors">
            <CardHeader>
              <TrendingUp className="h-12 w-12 text-primary mb-4" />
              <CardTitle>Performance Metrics</CardTitle>
              <CardDescription>
                Track KPIs across health, wealth, relationships, and skills with comparative analytics.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="border-2 hover:border-primary/50 transition-colors">
            <CardHeader>
              <Clock className="h-12 w-12 text-primary mb-4" />
              <CardTitle>Time Management</CardTitle>
              <CardDescription>
                Optimize your time allocation and boost productivity with detailed activity tracking.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="border-2 hover:border-primary/50 transition-colors">
            <CardHeader>
              <Zap className="h-12 w-12 text-primary mb-4" />
              <CardTitle>Smart Insights</CardTitle>
              <CardDescription>
                Get automated insights, recommendations, and celebrate achievements along the way.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
      </div>
    </div>
  )
}
