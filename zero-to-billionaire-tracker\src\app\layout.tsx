import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-sans",
});

export const metadata: Metadata = {
  title: "Zero to Billionaire Tracker",
  description: "A comprehensive personal development dashboard for tracking your journey to extraordinary success",
  keywords: ["personal development", "habit tracking", "goal setting", "financial tracking", "productivity"],
  authors: [{ name: "Zero to Billionaire Team" }],
  viewport: "width=device-width, initial-scale=1",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={inter.variable}>
      <body className="font-sans antialiased">
        {children}
      </body>
    </html>
  );
}
