import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  TrendingUp, 
  Target, 
  CheckSquare, 
  DollarSign, 
  Clock,
  Plus,
  ArrowUpRight,
  ArrowDownRight,
  Minus
} from 'lucide-react'

export default function DashboardPage() {
  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Welcome Section */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Welcome back!</h1>
            <p className="text-muted-foreground">Here's your progress overview for today.</p>
          </div>
          <Button variant="premium">
            <Plus className="h-4 w-4 mr-2" />
            Quick Add
          </Button>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Habits Completed</CardTitle>
              <CheckSquare className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">8/12</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-success flex items-center">
                  <ArrowUpRight className="h-3 w-3 mr-1" />
                  +2 from yesterday
                </span>
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Goals Progress</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">67%</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-success flex items-center">
                  <ArrowUpRight className="h-3 w-3 mr-1" />
                  +5% this week
                </span>
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Net Worth</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">$125,430</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-success flex items-center">
                  <ArrowUpRight className="h-3 w-3 mr-1" />
                  +$2,340 this month
                </span>
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Productivity Score</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">8.4/10</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-warning flex items-center">
                  <Minus className="h-3 w-3 mr-1" />
                  -0.2 from last week
                </span>
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Today's Habits */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>Today's Habits</CardTitle>
              <CardDescription>Complete your daily routines</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  { name: 'Morning Meditation', completed: true, streak: 12 },
                  { name: 'Exercise', completed: true, streak: 8 },
                  { name: 'Read for 30 minutes', completed: false, streak: 15 },
                  { name: 'Journal Writing', completed: false, streak: 5 },
                  { name: 'Learn New Skill', completed: true, streak: 3 },
                ].map((habit, index) => (
                  <div key={index} className="flex items-center justify-between p-3 rounded-lg border">
                    <div className="flex items-center space-x-3">
                      <div className={`w-4 h-4 rounded-full border-2 ${
                        habit.completed 
                          ? 'bg-success border-success' 
                          : 'border-muted-foreground'
                      }`} />
                      <div>
                        <p className={`font-medium ${habit.completed ? 'line-through text-muted-foreground' : ''}`}>
                          {habit.name}
                        </p>
                        <p className="text-xs text-muted-foreground">{habit.streak} day streak</p>
                      </div>
                    </div>
                    <Button variant="ghost" size="sm">
                      {habit.completed ? 'Undo' : 'Complete'}
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Quick Stats */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Active Goals</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Save $50,000</span>
                    <span className="text-sm font-medium">78%</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div className="bg-primary h-2 rounded-full" style={{ width: '78%' }} />
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Lose 20 lbs</span>
                    <span className="text-sm font-medium">45%</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div className="bg-primary h-2 rounded-full" style={{ width: '45%' }} />
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Learn Spanish</span>
                    <span className="text-sm font-medium">23%</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div className="bg-primary h-2 rounded-full" style={{ width: '23%' }} />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>This Week</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Habits completed</span>
                    <span className="text-sm font-medium">42/49</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Goals updated</span>
                    <span className="text-sm font-medium">5/7</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Productive hours</span>
                    <span className="text-sm font-medium">34.5h</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Net worth change</span>
                    <span className="text-sm font-medium text-success">+$1,240</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
