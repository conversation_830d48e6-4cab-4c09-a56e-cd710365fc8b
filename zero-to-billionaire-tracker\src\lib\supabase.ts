import { createClient } from '@supabase/supabase-js'
import { createClientComponentClient, createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// Client-side Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Client component Supabase client
export const createSupabaseClient = () => createClientComponentClient()

// Server component Supabase client
export const createSupabaseServerClient = () => createServerComponentClient({ cookies })

// Database types (will be generated from Supabase)
export type Database = {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          name: string
          avatar_url?: string
          theme_preference: string
          timezone: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          name: string
          avatar_url?: string
          theme_preference?: string
          timezone?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          name?: string
          avatar_url?: string
          theme_preference?: string
          timezone?: string
          updated_at?: string
        }
      }
      habits: {
        Row: {
          id: string
          user_id: string
          name: string
          description?: string
          category: string
          frequency: string
          target_days?: number
          current_streak: number
          longest_streak: number
          completion_rate: number
          is_active: boolean
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          description?: string
          category: string
          frequency: string
          target_days?: number
          current_streak?: number
          longest_streak?: number
          completion_rate?: number
          is_active?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          description?: string
          category?: string
          frequency?: string
          target_days?: number
          current_streak?: number
          longest_streak?: number
          completion_rate?: number
          is_active?: boolean
        }
      }
      habit_entries: {
        Row: {
          id: string
          habit_id: string
          date: string
          completed: boolean
          notes?: string
          created_at: string
        }
        Insert: {
          id?: string
          habit_id: string
          date: string
          completed: boolean
          notes?: string
          created_at?: string
        }
        Update: {
          id?: string
          habit_id?: string
          date?: string
          completed?: boolean
          notes?: string
        }
      }
    }
  }
}
